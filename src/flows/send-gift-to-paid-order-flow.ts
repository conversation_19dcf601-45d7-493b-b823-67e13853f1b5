import { OrderGift, OrderStatus } from "../mikerudenko/marketplace-shared";
import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { clearUserSession } from "../services/session";
import {
  getOrderById,
  getUniqueGift,
} from "../utils/business-connection-helpers";
import { validateSentGiftWithOrder } from "../utils/gift-validation";
import {
  logBuyerNotificationError,
  logBuyerNotificationSent,
  logGiftSentToRelayerSuccess,
  logGiftValidationError,
  logGiftValidationFailed,
  logOrderNotFound,
  logOrderNotPaidStatus,
  logSendGiftToPaidOrderStarted,
} from "./loggers/send-gift-to-paid-order-flow.logger";
import { handleGiftToRelayer } from "./shared/gift-to-relayer-handler";

export interface FlowContext {
  ctx: Context;
  chat_id: number;
  userId: string;
  pendingOrderId: string;
}

export const handleSendGiftToPaidOrder = async (
  flowContext: FlowContext,
  giftToTransfer: OrderGift
): Promise<boolean> => {
  const { ctx, chat_id, userId, pendingOrderId } = flowContext;

  logSendGiftToPaidOrderStarted({
    chat_id,
    userId,
    pendingOrderId,
  });

  // Get order and validate
  const order = await getOrderById(pendingOrderId);
  if (!order) {
    logOrderNotFound({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.ORDER_NOT_FOUND
    );
    return false;
  }

  // Check if this is a paid order (has buyer)
  if (!order.buyer_tg_id || order.status !== OrderStatus.PAID) {
    logOrderNotPaidStatus({
      chat_id,
      userId,
      pendingOrderId,
      currentStatus: order.status,
    });
    return false;
  }

  // Validate the sent gift with the order
  try {
    const uniqueGift = getUniqueGift(ctx);
    const isValid = await validateSentGiftWithOrder(
      order.collectionId,
      uniqueGift
    );

    if (!isValid) {
      logGiftValidationFailed({
        chat_id,
        userId,
        pendingOrderId,
        collectionId: order.collectionId,
        uniqueGift,
      });
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
      );
      return false;
    }
  } catch (error) {
    logGiftValidationError({
      chat_id,
      userId,
      pendingOrderId,
      error,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
    );
    return false;
  }

  // Process gift to relayer
  await handleGiftToRelayer(ctx, pendingOrderId, giftToTransfer, chat_id);

  // Send notification to buyer when gift is sent to relayer
  try {
    if (order.buyer_tg_id) {
      await ctx.telegram.sendMessage(
        parseInt(order.buyer_tg_id),
        MESSAGES.BUSINESS_CONNECTION.GIFT_READY_FOR_BUYER(order.number)
      );
      logBuyerNotificationSent({
        orderId: pendingOrderId,
        buyerTgId: order.buyer_tg_id,
        orderNumber: order.number,
      });
    }
  } catch (error) {
    logBuyerNotificationError({
      orderId: pendingOrderId,
      buyerTgId: order.buyer_tg_id,
      error,
    });
  }

  // Clear user session after successful processing
  await clearUserSession(userId);
  logGiftSentToRelayerSuccess({
    chat_id,
    userId,
    pendingOrderId,
    collectionId: order.collectionId,
  });

  return true;
};
