import { OrderGift, OrderStatus } from "../mikerudenko/marketplace-shared";
import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { clearUserSession } from "../services/session";
import {
  getOrderById,
  getUniqueGift,
} from "../utils/business-connection-helpers";
import { validateGiftForCreatedOrder } from "../utils/gift-validation";
import {
  logActivateOrderStarted,
  logGiftValidationError,
  logGiftValidationFailed,
  logOrderActivationSuccess,
  logOrderNotCreatedStatus,
  logOrderNotFound,
  logUserNotSeller,
} from "./loggers/activate-order-with-gift-flow.logger";
import { handleGiftToRelayer } from "./shared/gift-to-relayer-handler";

export interface FlowContext {
  ctx: Context;
  chat_id: number;
  userId: string;
  pendingOrderId: string;
}

export const handleActivateOrderWithGift = async (
  flowContext: FlowContext,
  giftToTransfer: OrderGift
): Promise<boolean> => {
  const { ctx, chat_id, userId, pendingOrderId } = flowContext;

  logActivateOrderStarted({
    chat_id,
    userId,
    pendingOrderId,
  });

  // Get order and validate
  const order = await getOrderById(pendingOrderId);
  if (!order) {
    logOrderNotFound({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.ORDER_NOT_FOUND
    );
    return false;
  }

  // Check if this is a created order needing activation
  if (order.status !== OrderStatus.CREATED) {
    logOrderNotCreatedStatus({
      chat_id,
      userId,
      pendingOrderId,
      currentStatus: order.status,
    });
    return false;
  }

  // Validate that current user is the seller
  if (order.seller_tg_id !== userId) {
    logUserNotSeller({
      chat_id,
      userId,
      pendingOrderId,
      sellerId: order.seller_tg_id as string,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      "You are not authorized to activate this order. Only the original seller can activate the order."
    );
    return false;
  }

  // Validate the sent gift for CREATED order
  try {
    const uniqueGift = getUniqueGift(ctx);
    const validationResult = await validateGiftForCreatedOrder(
      order.collectionId,
      uniqueGift
    );

    if (!validationResult.isValid) {
      logGiftValidationFailed({
        chat_id,
        userId,
        pendingOrderId,
        collectionId: order.collectionId,
        uniqueGift,
        validationMessage: validationResult.message as string,
      });

      // Send specific message if user is trying to trick us
      const messageToSend =
        validationResult.shouldReject && validationResult.message
          ? validationResult.message
          : MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT;

      await ctx.telegram.sendMessage(chat_id, messageToSend);
      return false;
    }
  } catch (error) {
    logGiftValidationError({
      chat_id,
      userId,
      pendingOrderId,
      error,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
    );
    return false;
  }

  // Process gift to relayer for activation
  await handleGiftToRelayer(ctx, pendingOrderId, giftToTransfer, chat_id);

  // Clear user session after successful processing
  await clearUserSession(userId);
  logOrderActivationSuccess({
    chat_id,
    userId,
    pendingOrderId,
    collectionId: order.collectionId,
  });

  return true;
};
