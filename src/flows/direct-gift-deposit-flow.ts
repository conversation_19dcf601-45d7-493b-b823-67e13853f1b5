import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { depositGiftDirectly } from "../firebase-service";
import { getGiftCollectionId } from "../utils/business-connection-helpers";
import { log } from "../utils/logger";
import { OrderGift } from "../mikerudenko/marketplace-shared";

export interface DirectDepositFlowContext {
  ctx: Context;
  chat_id: number;
  userId: string;
}

export const handleDirectGiftDeposit = async (
  flowContext: DirectDepositFlowContext,
  giftToTransfer: OrderGift
): Promise<boolean> => {
  const { ctx, chat_id, userId } = flowContext;

  log.info("Direct gift deposit flow started", {
    operation: "direct_gift_deposit_flow",
    chat_id,
    userId,
    gift: giftToTransfer,
  });

  try {
    // Get collection ID from the gift if available
    const collectionId = getGiftCollectionId(ctx);

    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.PROCESSING_GIFT
    );

    const result = await depositGiftDirectly(userId, giftToTransfer, collectionId);

    log.info("Direct gift deposit result", {
      operation: "direct_gift_deposit_flow",
      chat_id,
      userId,
      success: result.success,
    });

    if (result.success) {
      await ctx.telegram.sendMessage(
        chat_id,
        "🎁 Gift deposited successfully! You can now see your gift in the Pram app under 'My Gifts' tab."
      );
      
      log.info("Direct gift deposit completed successfully", {
        operation: "direct_gift_deposit_flow",
        chat_id,
        userId,
      });
      
      return true;
    } else {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_SENT_ERROR
      );
      
      log.warn("Direct gift deposit failed", {
        operation: "direct_gift_deposit_flow",
        chat_id,
        userId,
        result,
      });
      
      return false;
    }
  } catch (error) {
    log.error("Error in direct gift deposit flow", error, {
      operation: "direct_gift_deposit_flow",
      chat_id,
      userId,
      gift: giftToTransfer,
    });
    
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.GIFT_SENT_ERROR
    );
    
    return false;
  }
};
