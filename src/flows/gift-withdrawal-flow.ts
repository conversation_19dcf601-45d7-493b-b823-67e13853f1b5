import { Context } from "telegraf";
import { withdrawGiftByBot } from "../firebase-service";
import { clearSessionProperty } from "../services/session";
import { log } from "../utils/logger";
import { MESSAGES } from "../constants/messages";
import {
  getBusinessConnectionId,
  transferGift,
} from "../utils/business-connection-helpers";
import {
  isSimulationEnabled,
  getSimulationService,
} from "../services/simulation";

export interface GiftWithdrawalFlowContext {
  ctx: Context;
  chat_id: number;
  userId: string;
  withdrawalGiftId: string;
}

export const handleGiftWithdrawal = async (
  flowContext: GiftWithdrawalFlowContext
): Promise<boolean> => {
  const { ctx, chat_id, userId, withdrawalGiftId } = flowContext;

  try {
    log.info("Starting gift withdrawal flow", {
      operation: "gift_withdrawal_flow",
      chat_id,
      userId,
      withdrawalGiftId,
      simulationMode: isSimulationEnabled(),
    });

    // Check if simulation mode is enabled
    if (isSimulationEnabled()) {
      const simulationService = getSimulationService();
      await simulationService.handleGiftWithdrawalSimulation({
        ctx,
        tgId: userId,
        withdrawalGiftId,
      });

      // Clear the withdrawal gift ID from session
      await clearSessionProperty(userId, "withdrawal_gift_id");

      log.info("Gift withdrawal simulation completed", {
        operation: "gift_withdrawal_flow",
        chat_id,
        userId,
        withdrawalGiftId,
      });

      return true;
    }

    // Normal (non-simulation) flow
    // Send processing message
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.PROCESSING_WITHDRAWAL
    );

    // Call the withdrawal function
    const result = await withdrawGiftByBot({
      giftId: withdrawalGiftId,
      userTgId: userId,
    });

    log.info("Gift withdrawal result", {
      operation: "gift_withdrawal_flow",
      chat_id,
      userId,
      withdrawalGiftId,
      success: result.success,
    });

    if (result.success) {
      // Clear the withdrawal gift ID from session
      await clearSessionProperty(userId, "withdrawal_gift_id");

      // Send success message
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.WITHDRAWAL_SUCCESS
      );

      // Transfer the gift from relayer to user if ownedGiftId is available
      if (result.ownedGiftId) {
        const businessConnectionId = getBusinessConnectionId(ctx);
        if (businessConnectionId) {
          log.info("Transferring gift to user", {
            operation: "gift_withdrawal_flow",
            chat_id,
            userId,
            withdrawalGiftId,
            ownedGiftId: result.ownedGiftId,
            businessConnectionId,
          });

          await transferGift(
            ctx,
            businessConnectionId,
            chat_id,
            result.ownedGiftId
          );
        } else {
          log.warn("No business connection ID available for gift transfer", {
            operation: "gift_withdrawal_flow",
            chat_id,
            userId,
            withdrawalGiftId,
            ownedGiftId: result.ownedGiftId,
          });
        }
      }

      log.info("Gift withdrawal completed successfully", {
        operation: "gift_withdrawal_flow",
        chat_id,
        userId,
        withdrawalGiftId,
      });

      return true;
    } else {
      // Send error message
      const errorMessage = result.error || "Failed to withdraw gift";
      await ctx.telegram.sendMessage(
        chat_id,
        `${MESSAGES.BUSINESS_CONNECTION.WITHDRAWAL_ERROR}: ${errorMessage}`
      );

      log.warn("Gift withdrawal failed", {
        operation: "gift_withdrawal_flow",
        chat_id,
        userId,
        withdrawalGiftId,
        error: result.error,
      });

      return false;
    }
  } catch (error) {
    log.error("Error in gift withdrawal flow", error, {
      operation: "gift_withdrawal_flow",
      chat_id,
      userId,
      withdrawalGiftId,
    });

    // Send error message to user
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.WITHDRAWAL_ERROR
    );

    return false;
  }
};
