import { Context } from "telegraf";
import { depositGiftDirectly, withdrawGiftByBot } from "../../firebase-service";
import { generateMockGift } from "../mock-gift-service";
import {
  createOrderBackKeyboard,
  createMarketplaceInlineKeyboard,
} from "../../utils/keyboards";
import { CALLBACK_MESSAGES } from "../../handlers/callbacks/constants/messages";
import { log } from "../../utils/logger";
import { clearUserSession } from "../session";
import { OrderStatus } from "../../mikerudenko/marketplace-shared";

export interface SimulationConfig {
  isEnabled: boolean;
  defaultCollectionId: string;
}

export interface OrderSimulationParams {
  ctx: Context;
  order: any;
  orderId: string;
  isPaidOrderAwaitingGift: boolean;
  isCreatedOrderNeedingActivation: boolean;
  isBuyer: boolean;
  isSeller: boolean;
}

export interface GiftDepositSimulationParams {
  ctx: Context;
  tgId: string;
}

export interface GiftWithdrawalSimulationParams {
  ctx: Context;
  tgId: string;
  withdrawalGiftId: string;
}

export class SimulationService {
  private readonly config: SimulationConfig;

  constructor(config: SimulationConfig) {
    this.config = config;
  }

  isSimulationEnabled(): boolean {
    return this.config.isEnabled;
  }

  async handleOrderSimulation(params: OrderSimulationParams): Promise<void> {
    if (!this.config.isEnabled) {
      throw new Error("Simulation is not enabled");
    }

    const { ctx, order, isBuyer, isSeller } = params;
    let message = `📦 Order #${order.number}\n\n`;

    if (isBuyer && order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
      console.log(
        "isBuyer && order.status === OrderStatus.GIFT_SENT_TO_RELAYER"
      );
    } else if (isSeller && params.isPaidOrderAwaitingGift) {
      await this.handleSellerGiftDeposit(ctx, message);
    } else if (isSeller && params.isCreatedOrderNeedingActivation) {
      await this.handleSellerOrderActivation(ctx, message);
    } else {
      await this.handleGenericOrderView(ctx, order, message);
    }
  }

  async handleGiftDepositSimulation(
    params: GiftDepositSimulationParams
  ): Promise<void> {
    if (!this.config.isEnabled) {
      throw new Error("Simulation is not enabled");
    }

    const { ctx, tgId } = params;

    // Clear user session
    await this.clearUserSessionSafely(tgId);

    const message = `🎁 Deposit a Gift (Simulation Mode)

🔧 SIMULATION MODE: Generating and depositing a mock gift...`;

    await ctx.reply(message);

    try {
      const mockGift = generateMockGift();
      const result = await depositGiftDirectly(
        tgId,
        mockGift,
        this.config.defaultCollectionId
      );

      if (result.success) {
        const successMessage = this.formatGiftDepositSuccessMessage(mockGift);
        await ctx.reply(successMessage, createMarketplaceInlineKeyboard());

        log.info("Mock gift deposited successfully in simulation mode", {
          operation: "simulation_deposit_gift",
          tgId,
          giftId: result.giftId,
          collectionId: this.config.defaultCollectionId,
        });
      } else {
        await this.handleGiftDepositError(ctx, result.message);
      }
    } catch (error) {
      await this.handleGiftDepositError(ctx, "Failed to deposit mock gift");
      log.error("Error depositing mock gift in simulation mode", error, {
        operation: "simulation_deposit_gift_error",
        tgId,
      });
    }
  }

  async handleGiftWithdrawalSimulation(
    params: GiftWithdrawalSimulationParams
  ): Promise<void> {
    if (!this.config.isEnabled) {
      throw new Error("Simulation is not enabled");
    }

    const { ctx, tgId, withdrawalGiftId } = params;

    const message = `🎁 Gift Withdrawal (Simulation Mode)

🔧 SIMULATION MODE: Processing gift withdrawal...`;

    await ctx.reply(message);

    try {
      const result = await withdrawGiftByBot({
        giftId: withdrawalGiftId,
        userTgId: tgId,
      });

      if (result.success) {
        const successMessage = `✅ Gift withdrawal completed successfully in simulation mode!

🔧 SIMULATION MODE: Gift transfer skipped. In real mode, your gift would be transferred to you now.

${result.message || ""}`;

        await ctx.reply(successMessage, createMarketplaceInlineKeyboard());

        log.info("Gift withdrawal completed successfully in simulation mode", {
          operation: "simulation_gift_withdrawal",
          tgId,
          withdrawalGiftId,
          ownedGiftId: result.ownedGiftId,
        });
      } else {
        const errorMessage = `❌ Gift withdrawal failed in simulation mode: ${
          result.error || "Unknown error"
        }`;
        await ctx.reply(errorMessage, createMarketplaceInlineKeyboard());

        log.warn("Gift withdrawal failed in simulation mode", {
          operation: "simulation_gift_withdrawal",
          tgId,
          withdrawalGiftId,
          error: result.error,
        });
      }
    } catch (error) {
      const errorMessage =
        "❌ Failed to process gift withdrawal in simulation mode";
      await ctx.reply(errorMessage, createMarketplaceInlineKeyboard());

      log.error("Error processing gift withdrawal in simulation mode", error, {
        operation: "simulation_gift_withdrawal_error",
        tgId,
        withdrawalGiftId,
      });
    }
  }

  private async handleSellerGiftDeposit(
    ctx: Context,
    baseMessage: string
  ): Promise<void> {
    let message = baseMessage;
    message += CALLBACK_MESSAGES.GROUP1_ORDER_READY;
    message += `\n\n🔧 DEV MODE: This is a simulation. In production, you would send the gift to @premrelayer.`;

    await ctx.reply(message, createOrderBackKeyboard());
  }

  private async handleSellerOrderActivation(
    ctx: Context,
    baseMessage: string
  ): Promise<void> {
    let message = baseMessage;
    message += CALLBACK_MESSAGES.GROUP2_ORDER_READY;
    message += `\n\n🔧 DEV MODE: This is a simulation. In production, you would need to deposit a gift first.`;

    await ctx.reply(message, createOrderBackKeyboard());
  }

  private async handleGenericOrderView(
    ctx: Context,
    order: any,
    baseMessage: string
  ): Promise<void> {
    let message = baseMessage;
    message += `Status: ${order.status}\n`;
    message += `Price: ${order.price} TON\n\n`;
    message += `🔧 DEV MODE: This order is in simulation mode.`;

    await ctx.reply(message, createOrderBackKeyboard());
  }

  private async clearUserSessionSafely(tgId: string): Promise<void> {
    try {
      await clearUserSession(tgId);
    } catch (error) {
      log.warn("Failed to clear user session during simulation", {
        operation: "simulation_clear_session",
        tgId,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private formatGiftDepositSuccessMessage(mockGift: any): string {
    return `✅ Mock gift deposited successfully!

🎁 Gift Details:
• Name: ${mockGift.base_name}
• Model: ${mockGift.model.name}
• Symbol: ${mockGift.symbol.name}
• Backdrop: ${mockGift.backdrop.name}

Your gift is now available in the Pram app under 'My Gifts' tab.`;
  }

  private async handleGiftDepositError(
    ctx: Context,
    errorMessage?: string
  ): Promise<void> {
    const message = `❌ Failed to deposit mock gift: ${
      errorMessage || "Unknown error"
    }

Please try again or contact support if the issue persists.`;

    await ctx.reply(message, createMarketplaceInlineKeyboard());
  }
}
