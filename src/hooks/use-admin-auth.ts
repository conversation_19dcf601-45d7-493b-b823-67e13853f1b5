'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Role } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

export const useAdminAuth = () => {
  const { currentUser } = useRootContext();
  const router = useRouter();

  useEffect(() => {
    if (!currentUser) {
      router.push('/admin/auth');
      return;
    }

    if (currentUser.role !== Role.ADMIN) {
      router.push('/');
    }
  }, [currentUser, router]);

  const isAuthorized = currentUser && currentUser.role === Role.ADMIN;

  return {
    currentUser,
    isAuthorized,
    isLoading: !currentUser,
  };
};
