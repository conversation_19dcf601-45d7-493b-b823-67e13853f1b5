import { log } from "./logger";

export function logGetOrderError(params: {
  error: unknown;
  orderId: string;
}) {
  log.error("Error getting order.", params.error, {
    operation: "business_connection",
    component: "get_order_by_id",
    orderId: params.orderId,
  });
}

export function logGiftTransferSuccess(params: {
  chatId: string;
  owned_gift_id: string;
}) {
  log.info(`Successfully sent gift back to user ${params.chatId}`, {
    operation: "business_connection",
    component: "transfer_gift",
    chatId: params.chatId,
    owned_gift_id: params.owned_gift_id,
  });
}

export function logGiftTransferFailed(params: {
  sendGiftResult: any;
  chatId: string;
  owned_gift_id: string;
}) {
  log.error("Failed to transfer gift", params.sendGiftResult, {
    operation: "business_connection",
    component: "transfer_gift",
    chatId: params.chatId,
    owned_gift_id: params.owned_gift_id,
  });
}

export function logGiftTransferError(params: {
  error: unknown;
  chatId: string;
  owned_gift_id: string;
}) {
  log.error("Error transferring gift", params.error, {
    operation: "business_connection",
    component: "transfer_gift",
    chatId: params.chatId,
    owned_gift_id: params.owned_gift_id,
  });
}

export function logBusinessAccountGiftsError(params: {
  error: unknown;
  businessConnectionId: string;
}) {
  log.error("Error getting business account gifts", params.error, {
    operation: "business_connection",
    component: "get_business_account_gifts",
    businessConnectionId: params.businessConnectionId,
  });
}
