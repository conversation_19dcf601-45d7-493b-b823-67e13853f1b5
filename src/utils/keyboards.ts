import { Markup } from "telegraf";
import { BUTTON_TEXTS } from "../constants/messages";
import { loadEnvironment } from "../config/env-loader";
import { WEB_APP_URL } from "../app.constants";

loadEnvironment();

export const createMainKeyboard = () => {
  return Markup.keyboard([
    [
      Markup.button.text(BUTTON_TEXTS.DEPOSIT_GIFT),
      Markup.button.text(BUTTON_TEXTS.MY_GIFTS),
    ],
    [
      Markup.button.text(BUTTON_TEXTS.MY_SELL_ORDERS),
      Markup.button.text(BUTTON_TEXTS.CONTACT_SUPPORT),
    ],
  ]).resize();
};

export const createMarketplaceInlineKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderHelpKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
    [Markup.button.callback("📋 Order Help", "order_help")],
  ]);
};

export const createSupportKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
    [Markup.button.callback(BUTTON_TEXTS.CONTACT_SUPPORT, "contact_support")],
  ]);
};

export const createOrderActionsKeyboard = (orderId: string) => {
  return Markup.inlineKeyboard([
    [
      Markup.button.callback(
        "🎁 I'm ready to send gift",
        `complete_${orderId}`
      ),
    ],
    [Markup.button.callback(BUTTON_TEXTS.BACK_TO_ORDERS, "back_to_orders")],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderBackKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.BACK_TO_ORDERS, "back_to_orders")],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderCompletionKeyboard = (orderId: string) => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.CANCEL, `order_${orderId}`)],
  ]);
};

export const createOrderSuccessKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.VIEW_MY_ORDERS, "back_to_orders")],
    [Markup.button.webApp(BUTTON_TEXTS.OPEN_MARKETPLACE, WEB_APP_URL)],
  ]);
};

export const createOrderErrorKeyboard = () => {
  return Markup.inlineKeyboard([
    [Markup.button.callback(BUTTON_TEXTS.VIEW_MY_ORDERS, "back_to_orders")],
    [Markup.button.callback(BUTTON_TEXTS.CONTACT_SUPPORT, "contact_support")],
  ]);
};

export { WEB_APP_URL };
