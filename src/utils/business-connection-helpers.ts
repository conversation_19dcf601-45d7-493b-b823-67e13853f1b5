import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { getOrderByIdByBot } from "../firebase-service";
import { loadEnvironment } from "../config/env-loader";
import { BOT_TOKEN } from "../app.constants";
import { OrderGift } from "../mikerudenko/marketplace-shared";
import {
  logGetOrderError,
  logGiftTransferSuccess,
  logGiftTransferFailed,
  logGiftTransferError,
} from "./business-connection-helpers.logger";

loadEnvironment();

interface OrderData {
  id: string;
  collectionId: string;
  buyerId?: string;
  sellerId?: string;
  buyer_tg_id?: string;
  seller_tg_id?: string;
  status: string;
  [key: string]: any;
}

const isGiftComplete = (fullGift: any): boolean => {
  if (!fullGift?.gift) return false;

  const gift = fullGift.gift;

  // Check if all required fields exist
  const hasModel =
    gift.model?.name && typeof gift.model.rarity_per_mille === "number";
  const hasSymbol =
    gift.symbol?.name && typeof gift.symbol.rarity_per_mille === "number";
  const hasBackdrop =
    gift.backdrop?.name &&
    gift.backdrop?.colors &&
    typeof gift.backdrop.rarity_per_mille === "number";

  return hasModel && hasSymbol && hasBackdrop;
};

export const getGiftToTransfer = (ctx: Context): OrderGift | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  const fullGift = businessMessage?.unique_gift.gift;

  if (!fullGift) {
    return undefined;
  }

  // Skip gifts that don't have model, symbol, or backdrop fields
  if (!isGiftComplete(fullGift)) {
    return undefined;
  }

  return {
    base_name: fullGift.gift.base_name,
    owned_gift_id: fullGift.owned_gift_id,
    backdrop: fullGift.gift.backdrop,
    model: {
      name: fullGift.gift.model.name,
      rarity_per_mille: fullGift.gift.model.rarity_per_mille,
    },
    symbol: {
      name: fullGift.gift.symbol.name,
      rarity_per_mille: fullGift.gift.symbol.rarity_per_mille,
    },
  };
};

export const getGiftCollectionId = (ctx: Context): string | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  const collectionId = businessMessage?.unique_gift?.gift?.gift?.id;
  return collectionId;
};

export const getBusinessConnectionId = (ctx: Context): string | undefined => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.business_connection_id;
};

export const getUniqueGift = (ctx: Context): any => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.unique_gift;
};

export const getOrderById = async (
  orderId: string
): Promise<OrderData | null> => {
  try {
    const result = await getOrderByIdByBot(orderId);
    if (result.success && result.order) {
      return result.order as OrderData;
    }
    return null;
  } catch (error) {
    logGetOrderError({
      error,
      orderId,
    });
    return null;
  }
};

export const transferGift = async (
  ctx: Context,
  businessConnectionId: string,
  chatId: number,
  owned_gift_id: string
): Promise<void> => {
  try {
    const sendGiftResponse = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          new_owner_chat_id: chatId,
          star_count: 25,
          owned_gift_id,
        }),
      }
    );

    const sendGiftResult = (await sendGiftResponse.json()) as {
      ok: boolean;
      description?: string;
      error_code?: number;
    };

    if (sendGiftResult.ok) {
      logGiftTransferSuccess({
        chatId: String(chatId),
        owned_gift_id,
      });
      await ctx.telegram.sendMessage(
        chatId,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFERRED_SUCCESS
      );
    } else {
      logGiftTransferFailed({
        sendGiftResult,
        chatId: String(chatId),
        owned_gift_id,
      });
      await ctx.telegram.sendMessage(
        chatId,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_GENERIC_ERROR
      );
    }
  } catch (error) {
    logGiftTransferError({
      error,
      chatId: String(chatId),
      owned_gift_id,
    });
    await ctx.telegram.sendMessage(
      chatId,
      MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_GENERIC_ERROR
    );
  }
};
