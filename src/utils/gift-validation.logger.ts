import { log } from "./logger";

export function logCdnMappingFetchFailed(params: {
  operation: string;
  component: string;
  orderCollectionId?: string;
}) {
  log.warn(
    "Failed to fetch collection mapping from CDN, skipping gift validation for CREATED order",
    {
      ...params,
    }
  );
}

export function logCollectionNotFound(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
}) {
  log.error(
    `Collection name not found for ID: ${params.orderCollectionId}`,
    undefined,
    {
      ...params,
    }
  );
}

export function logGiftValidationStarted(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
  collectionName: string;
  giftBaseName: string;
}) {
  log.info("Validating gift for CREATED order", {
    ...params,
  });
}

export function logGiftNameMatchesCollection(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
  collectionName: string;
  giftBaseName: string;
}) {
  log.warn("Gift base name matches collection name - user trying to trick us", {
    ...params,
  });
}

export function logGiftValidationError(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
  error: unknown;
}) {
  log.error("Error validating gift for CREATED order", params.error, {
    operation: params.operation,
    component: params.component,
    orderCollectionId: params.orderCollectionId,
  });
}

export function logGiftValidationSuccess(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
  collectionName: string;
  giftBaseName: string;
}) {
  log.info("Gift validation successful for CREATED order", {
    ...params,
  });
}

export function logCdnFetchError(params: {
  operation: string;
  component: string;
  error: unknown;
}) {
  log.error("Error fetching collection mapping from CDN", params.error, {
    operation: params.operation,
    component: params.component,
  });
}

export function logGiftValidationForOrderStarted(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
  giftId?: string;
}) {
  log.info("Starting gift validation for order", {
    ...params,
  });
}

export function logGiftValidationForOrderSuccess(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
  isValid: boolean;
}) {
  log.info("Gift validation completed for order", {
    ...params,
  });
}

export function logGiftValidationForOrderError(params: {
  operation: string;
  component: string;
  orderCollectionId: string;
  error: unknown;
}) {
  log.error("Error validating gift for order", params.error, {
    operation: params.operation,
    component: params.component,
    orderCollectionId: params.orderCollectionId,
  });
}

export function logIdToNameMappingFetchError(params: { error: unknown }) {
  log.error("Error fetching id-to-name mapping from CDN", params.error, {
    operation: "gift_validation",
    component: "fetch_id_to_name_mapping",
  });
}

export function logAttributeDataFetchError(params: {
  error: unknown;
  attributeType: string;
  collectionName: string;
}) {
  log.error(
    `Error fetching ${params.attributeType} data for collection ${params.collectionName} from CDN`,
    params.error,
    {
      operation: "gift_validation",
      component: "fetch_attribute_data",
      attributeType: params.attributeType,
      collectionName: params.collectionName,
    }
  );
}

export function logInvalidAttribute(params: {
  attributeType: string;
  giftAttributeName: string;
  giftRarityPermille: number;
  collectionName?: string;
}) {
  log.error(
    `Invalid ${params.attributeType}: ${params.giftAttributeName} with rarity ${params.giftRarityPermille} not found`,
    undefined,
    {
      operation: "gift_validation",
      component: "validate_attribute",
      attributeType: params.attributeType,
      giftAttributeName: params.giftAttributeName,
      giftRarityPermille: params.giftRarityPermille,
      collectionName: params.collectionName,
    }
  );
}

export function logCollectionMappingFetchFailed(params: {
  orderCollectionId: string;
}) {
  log.warn(
    "Failed to fetch collection mapping from CDN, skipping gift validation",
    {
      operation: "gift_validation",
      component: "validate_sent_gift_with_order",
      orderCollectionId: params.orderCollectionId,
    }
  );
}

export function logCollectionNameNotFound(params: {
  orderCollectionId: string;
}) {
  log.error(
    `Collection name not found for ID: ${params.orderCollectionId}`,
    undefined,
    {
      operation: "gift_validation",
      component: "validate_sent_gift_with_order",
      orderCollectionId: params.orderCollectionId,
    }
  );
}

export function logModelsDataFetchFailed(params: { collectionName: string }) {
  log.warn("Failed to fetch models data from CDN, skipping gift validation", {
    operation: "gift_validation",
    component: "validate_sent_gift_with_order",
    collectionName: params.collectionName,
  });
}

export function logPatternsDataFetchFailed(params: { collectionName: string }) {
  log.warn("Failed to fetch patterns data from CDN, skipping gift validation", {
    operation: "gift_validation",
    component: "validate_sent_gift_with_order",
    collectionName: params.collectionName,
  });
}

export function logBackdropsDataFetchFailed(params: {
  collectionName: string;
}) {
  log.warn(
    "Failed to fetch backdrops data from CDN, skipping gift validation",
    {
      operation: "gift_validation",
      component: "validate_sent_gift_with_order",
      collectionName: params.collectionName,
    }
  );
}

export function logBackdropValidationSuccess(params: {
  collectionName: string;
}) {
  log.info("Backdrop is valid", {
    operation: "gift_validation",
    component: "validate_sent_gift_with_order",
    collectionName: params.collectionName,
  });
}

export function logGiftValidationFailed(params: {
  error: unknown;
  orderCollectionId: string;
}) {
  log.error("Gift validation failed", params.error, {
    operation: "gift_validation",
    component: "validate_sent_gift_with_order",
    orderCollectionId: params.orderCollectionId,
  });
}
