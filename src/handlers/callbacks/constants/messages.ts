import { PREM_RELAYER_USERNAME } from "../../../app.constants";

export const CALLBACK_MESSAGES = {
  // Common messages
  TELEGRAM_ID_ERROR:
    "❌ Unable to identify your Telegram ID. Please try again.",
  ORDER_NOT_FOUND:
    "❌ Order not found. It may have been completed or cancelled.",
  GENERIC_ERROR: "❌ Failed to process your request. Please try again later.",

  // Order Help
  ORDER_HELP: `📋 Order Help

If you need help with your order:

1. Open the marketplace using the menu button
2. Go to your profile/orders section
3. Find your order and follow the instructions
4. Contact support if you encounter issues

For immediate assistance, you can also contact our support team.`,

  // Contact Support
  CONTACT_SUPPORT: `📞 Contact Support

You can reach our support team through:

• Telegram: https://t.me/prem_support_official
• Live chat in the web app

We typically respond within 24 hours.`,

  // Marketplace
  OPENING_MARKETPLACE: `🌐 Opening Marketplace

Click the button below to access the full marketplace:`,

  // Back to Menu
  BACK_TO_MENU: `🔙 Back to Menu

You can use the buttons below to navigate or open the marketplace:`,

  // Order Types
  CHOOSE_ORDER_TYPE: "📋 Choose which orders to view:",

  // Buy Orders
  NO_BUY_ORDERS: `📭 You don't have any buy orders yet.

Create your first buy order using the marketplace web app!`,

  BUY_ORDERS_TITLE: (count: number) =>
    `🛒 Your Buy Orders (${count} total)\n\n`,

  // Sell Orders
  NO_SELL_ORDERS: `📭 You don't have any sell orders yet.

Create your first sell order using the marketplace web app!`,

  SELL_ORDERS_TITLE: `💰 Your Sell Orders\n\n`,

  NO_ACTIONABLE_SELL_ORDERS: `📭 You don't have any actionable sell orders.

Create your first sell order using the marketplace web app!`,

  // Sell Order Groups
  ORDERS_READY_FOR_COMPLETION: (count: number) =>
    `🟠 Orders ready for completion: ${count}\n`,
  ORDERS_NEED_ACTIVATION: (count: number) =>
    `🔵 Orders need gift to activate: ${count}\n`,
  CANCELLED_ORDERS_WITH_GIFTS: (count: number) =>
    `🔴 Cancelled orders with gifts: ${count}\n`,

  // Group specific messages
  GROUP1_TITLE: (count: number) =>
    `🟠 Paid Orders (${count} total)\n\nThese orders are paid and waiting for you to send the gift:\n\n`,
  GROUP2_TITLE: (count: number) =>
    `🔵 Created Orders (${count} total)\n\nThese orders need a gift to be activated:\n\n`,
  GROUP3_TITLE: (count: number) =>
    `🔴 Cancelled Orders (${count} total)\n\nThese orders have gifts that can be refunded:\n\n`,

  NO_GROUP1_ORDERS: `📭 No paid orders waiting for gift.`,
  NO_GROUP2_ORDERS: `📭 No created orders for MARKET collections.`,
  NO_GROUP3_ORDERS: `📭 No cancelled orders with gifts.`,

  // Order completion messages
  GIFT_READY_FOR_DELIVERY: `🎁 Your gift is ready for delivery!

Please go to ${PREM_RELAYER_USERNAME} now and write in chat "Get a gift"`,

  GROUP1_ORDER_READY: `🟠 This order is ready for completion!

To complete this order:
1. Send the gift/item to this Prem Gift Relayer
2. The bot will verify and complete the purchase
3. Funds will be transferred to the seller

⚠️ Only send the gift when you're ready to complete the order!`,

  GROUP2_ORDER_READY: `🔵 This order needs a gift to be activated!

To activate this order:
1. Send the gift/item to this Prem Gift Relayer
2. The bot will verify and activate the order
3. Order will become available for buyers

⚠️ Send the gift to make your order active!`,

  GROUP3_ORDER_READY: `🔴 This order was cancelled and has a gift for refund!

To get your gift back:
1. Go to ${PREM_RELAYER_USERNAME}
2. Write "get a gift" in the chat
3. You will receive your gift back

💡 Your gift is ready for pickup!`,

  // Development mode messages
  DEV_ORDER_READY: (isActivation: boolean) =>
    `🎁 This order is ${
      isActivation ? "ready for activation" : "ready for completion"
    }!

🔧 DEV MODE: Simulating gift ${
      isActivation ? "activation" : "sending to relayer"
    }...`,

  DEV_SUCCESS: (isActivation: boolean) =>
    `\n\n✅ DEV MODE: Gift ${
      isActivation ? "added and order activated" : "sent to relayer"
    } successfully!`,

  DEV_ERROR: (isActivation: boolean) =>
    `\n\n❌ DEV MODE: Error ${
      isActivation ? "activating order" : "sending gift to relayer"
    }.`,

  DEV_PURCHASE_SUCCESS: `\n\n✅ DEV MODE: Purchase completed successfully!`,
  DEV_PURCHASE_ERROR: `\n\n❌ DEV MODE: Error completing purchase.`,
  DEV_NO_ACTIONS: `🔧 DEV MODE: No actions available for this order status.`,

  // Gift completion
  GIFT_RECEIVED_SUCCESS: `🎉 Gift received successfully!

Your order has been completed. Thank you for using our marketplace!`,

  // Error messages
  FETCH_BUY_ORDERS_ERROR:
    "❌ Failed to fetch your buy orders. Please try again later.",
  FETCH_SELL_ORDERS_ERROR:
    "❌ Failed to fetch your sell orders. Please try again later.",
  SHOW_ORDER_OPTIONS_ERROR:
    "❌ Failed to show order options. Please try again later.",
  FETCH_GROUP_ORDERS_ERROR:
    "❌ Failed to fetch orders. Please try again later.",

  // Status messages
  ORDER_STATUS_MESSAGES: {
    active: "This order is waiting for a buyer.",
    paid: "This order has been paid and is being processed.",
    fulfilled: "This order has been completed.",
    cancelled: "This order has been cancelled.",
    created: "This order has been created and is waiting for activation.",
  },
} as const;
