import { Context, Markup } from "telegraf";
import {
  formatOrderForDisplay,
  getUserOrdersByTgId,
} from "../../firebase-service";
import { createMarketplaceInlineKeyboard } from "../../utils/keyboards";
import { CALLBACK_MESSAGES } from "./constants/messages";
import { log } from "../../utils/logger";
import { OrderStatus } from "../../mikerudenko/marketplace-shared";

export const handleViewBuyOrdersCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(CALLBACK_MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.buyOrdersCount === 0) {
      ctx.editMessageText(
        CALLBACK_MESSAGES.NO_BUY_ORDERS,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const buyOrders = ordersResponse.buyOrders;
    let message = CALLBACK_MESSAGES.BUY_ORDERS_TITLE(buyOrders.length);

    // Create inline keyboard with order buttons - show only gift ready orders for buyers
    const giftReadyOrders = buyOrders.filter(
      (order) => order.status === OrderStatus.GIFT_SENT_TO_RELAYER
    );

    const orderButtons = giftReadyOrders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🔙 Back to Order Types", "back_to_orders"),
    ]);
    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (buyOrders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    log.error("Error fetching user buy orders", error, {
      operation: "fetch_buy_orders",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(
      CALLBACK_MESSAGES.FETCH_BUY_ORDERS_ERROR,
      createMarketplaceInlineKeyboard()
    );
  }
};
