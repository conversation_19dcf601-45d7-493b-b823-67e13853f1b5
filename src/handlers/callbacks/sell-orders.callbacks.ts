import { Context, Markup } from "telegraf";
import {
  formatOrderForDisplay,
  getUserOrdersByTgId,
} from "../../firebase-service";
import { createMarketplaceInlineKeyboard } from "../../utils/keyboards";
import { CALLBACK_MESSAGES } from "./constants/messages";
import { log } from "../../utils/logger";

export const handleViewSellOrdersCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(CALLBACK_MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.sellOrdersCount === 0) {
      ctx.editMessageText(
        CALLBACK_MESSAGES.NO_SELL_ORDERS,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    // Show 3 groups of sell orders
    let message = CALLBACK_MESSAGES.SELL_ORDERS_TITLE;
    const groupButtons = [];

    // Paid orders waiting for gift
    if (ordersResponse.paidOrdersAwaitingGiftCount > 0) {
      message += CALLBACK_MESSAGES.ORDERS_READY_FOR_COMPLETION(
        ordersResponse.paidOrdersAwaitingGiftCount
      );
      groupButtons.push([
        Markup.button.callback(
          `🟠 Paid Orders (${ordersResponse.paidOrdersAwaitingGiftCount})`,
          "paid_orders_awaiting_gift"
        ),
      ]);
    }

    // Created orders for MARKET collections
    if (ordersResponse.createdOrdersNeedingActivationCount > 0) {
      message += CALLBACK_MESSAGES.ORDERS_NEED_ACTIVATION(
        ordersResponse.createdOrdersNeedingActivationCount
      );
      groupButtons.push([
        Markup.button.callback(
          `🔵 Created Orders (${ordersResponse.createdOrdersNeedingActivationCount})`,
          "created_orders_needing_activation"
        ),
      ]);
    }

    // Cancelled orders with gifts for refund
    if (ordersResponse.cancelledOrdersWithGiftsCount > 0) {
      message += CALLBACK_MESSAGES.CANCELLED_ORDERS_WITH_GIFTS(
        ordersResponse.cancelledOrdersWithGiftsCount
      );
      groupButtons.push([
        Markup.button.callback(
          `🔴 Cancelled Orders (${ordersResponse.cancelledOrdersWithGiftsCount})`,
          "cancelled_orders_with_gifts"
        ),
      ]);
    }

    if (groupButtons.length === 0) {
      ctx.editMessageText(
        CALLBACK_MESSAGES.NO_ACTIONABLE_SELL_ORDERS,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    // Add navigation buttons
    groupButtons.push([
      Markup.button.callback("🔙 Back to Order Types", "back_to_orders"),
    ]);
    groupButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    ctx.editMessageText(message, Markup.inlineKeyboard(groupButtons));
  } catch (error) {
    log.error("Error fetching user sell orders", error, {
      operation: "fetch_sell_orders",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(
      CALLBACK_MESSAGES.FETCH_SELL_ORDERS_ERROR,
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handlePaidOrdersAwaitingGiftCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(CALLBACK_MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (
      !ordersResponse.success ||
      ordersResponse.paidOrdersAwaitingGiftCount === 0
    ) {
      ctx.editMessageText(
        CALLBACK_MESSAGES.NO_GROUP1_ORDERS,
        Markup.inlineKeyboard([
          [
            Markup.button.callback(
              "🔙 Back to Sell Orders",
              "view_sell_orders"
            ),
          ],
        ])
      );
      return;
    }

    let message = CALLBACK_MESSAGES.GROUP1_TITLE(
      ordersResponse.paidOrdersAwaitingGiftCount
    );

    const orderButtons = ordersResponse.paidOrdersAwaitingGift
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🔙 Back to Sell Orders", "view_sell_orders"),
    ]);

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    log.error("Error fetching sell orders group 1", error, {
      operation: "fetch_sell_orders_group_1",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(CALLBACK_MESSAGES.FETCH_GROUP_ORDERS_ERROR);
  }
};

export const handleCreatedOrdersNeedingActivationCallback = async (
  ctx: Context
) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(CALLBACK_MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (
      !ordersResponse.success ||
      ordersResponse.createdOrdersNeedingActivationCount === 0
    ) {
      ctx.editMessageText(
        CALLBACK_MESSAGES.NO_GROUP2_ORDERS,
        Markup.inlineKeyboard([
          [
            Markup.button.callback(
              "🔙 Back to Sell Orders",
              "view_sell_orders"
            ),
          ],
        ])
      );
      return;
    }

    let message = CALLBACK_MESSAGES.GROUP2_TITLE(
      ordersResponse.createdOrdersNeedingActivationCount
    );

    const orderButtons = ordersResponse.createdOrdersNeedingActivation
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🔙 Back to Sell Orders", "view_sell_orders"),
    ]);

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    log.error("Error fetching sell orders group 2", error, {
      operation: "fetch_sell_orders_group_2",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(CALLBACK_MESSAGES.FETCH_GROUP_ORDERS_ERROR);
  }
};

export const handleCancelledOrdersWithGiftsCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(CALLBACK_MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (
      !ordersResponse.success ||
      ordersResponse.cancelledOrdersWithGiftsCount === 0
    ) {
      ctx.editMessageText(
        CALLBACK_MESSAGES.NO_GROUP3_ORDERS,
        Markup.inlineKeyboard([
          [
            Markup.button.callback(
              "🔙 Back to Sell Orders",
              "view_sell_orders"
            ),
          ],
        ])
      );
      return;
    }

    let message = CALLBACK_MESSAGES.GROUP3_TITLE(
      ordersResponse.cancelledOrdersWithGiftsCount
    );

    const orderButtons = ordersResponse.cancelledOrdersWithGifts
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🔙 Back to Sell Orders", "view_sell_orders"),
    ]);

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    log.error("Error fetching sell orders group 3", error, {
      operation: "fetch_sell_orders_group_3",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(CALLBACK_MESSAGES.FETCH_GROUP_ORDERS_ERROR);
  }
};
