import { Context } from "telegraf";
import { createSupportKeyboard, createMarketplaceInlineKeyboard } from "../../utils/keyboards";
import { CALLBACK_MESSAGES } from "./constants/messages";

/**
 * Handle order help callback
 */
export const handleOrderHelpCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(CALLBACK_MESSAGES.ORDER_HELP, createSupportKeyboard());
};

/**
 * Handle contact support callback
 */
export const handleContactSupportCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(CALLBACK_MESSAGES.CONTACT_SUPPORT, createMarketplaceInlineKeyboard());
};
