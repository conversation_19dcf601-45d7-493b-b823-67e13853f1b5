import { Context, Markup } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../../utils/keyboards";
import { CALLBACK_MESSAGES } from "./constants/messages";
import { log } from "../../utils/logger";

export const handleOpenMarketplaceCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    CALLBACK_MESSAGES.OPENING_MARKETPLACE,
    createMarketplaceInlineKeyboard()
  );
};

export const handleBackToMenuCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(CALLBACK_MESSAGES.BACK_TO_MENU, createMainKeyboard());
};

export const handleBackToOrdersCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const orderTypeButtons = [
      [
        Markup.button.callback("🛒 Buy Orders", "view_buy_orders"),
        Markup.button.callback("💰 Sell Orders", "view_sell_orders"),
      ],
      [Markup.button.callback("🌐 Open Marketplace", "open_marketplace")],
    ];

    ctx.editMessageText(
      CALLBACK_MESSAGES.CHOOSE_ORDER_TYPE,
      Markup.inlineKeyboard(orderTypeButtons)
    );
  } catch (error) {
    log.error("Error showing order options", error, {
      operation: "show_order_options",
      chatId: ctx.chat?.id as number,
      userId: ctx.from?.id as number,
    });
    ctx.reply(
      CALLBACK_MESSAGES.SHOW_ORDER_OPTIONS_ERROR,
      createMarketplaceInlineKeyboard()
    );
  }
};
