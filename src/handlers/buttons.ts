import { Context, Markup } from "telegraf";
import { PREM_RELAYER_USERNAME } from "../app.constants";
import { BUTTON_TEXTS, MESSAGES } from "../constants/messages";
import {
  formatOrderForDisplay,
  getUserOrdersByTgId,
} from "../firebase-service";
import { clearUserSession } from "../services/session";
import { createMarketplaceInlineKeyboard } from "../utils/keyboards";
import { log } from "../utils/logger";
import {
  getSimulationService,
  isSimulationEnabled,
} from "../services/simulation";
import { logUserSellOrdersError } from "./handlers.logger";

export const handleGetMyGiftsButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    ctx.reply("🎁 Fetching your gifts...");

    // TODO: Call getUserGiftsAvailableForWithdrawal cloud function
    console.log(`My Gifts button clicked by user: ${tgId}`);

    ctx.reply(
      "🎁 My Gifts feature is coming soon! This will show your gifts available for withdrawal.",
      createMarketplaceInlineKeyboard()
    );
  } catch (error) {
    log.error("Error in handleGetMyGiftsButton", {
      error,
      userId: String(ctx.from?.id),
      chatId: String(ctx.chat?.id),
    });
    ctx.reply(
      "❌ Error fetching your gifts. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleGetMySellOrdersButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    ctx.reply(MESSAGES.ORDERS.FETCHING_SELL);

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.sellOrdersCount === 0) {
      ctx.reply(
        MESSAGES.ORDERS.NO_SELL_ORDERS,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const sellOrders = ordersResponse.sellOrders;
    const completableOrders = sellOrders.filter(
      (order) => order.status === "paid"
    );
    const giftReadyOrders = sellOrders.filter(
      (order) => order.status === "gift_sent_to_relayer"
    );

    let message = `${MESSAGES.ORDERS.SELL_ORDERS_TITLE(sellOrders.length)}

`;

    if (completableOrders.length > 0) {
      message += `${MESSAGES.ORDERS.ORDERS_READY_FOR_COMPLETION(
        completableOrders.length
      )}

`;
    }

    if (giftReadyOrders.length > 0) {
      message += `${MESSAGES.ORDERS.GIFTS_READY_FOR_DELIVERY(
        giftReadyOrders.length
      )}

`;
    }

    const orderButtons = sellOrders
      .slice(0, 10)
      .filter((order) => order.status === "paid")
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback(BUTTON_TEXTS.OPEN_MARKETPLACE, "open_marketplace"),
    ]);

    if (sellOrders.length > 10) {
      message += `
${MESSAGES.ORDERS.SHOWING_LIMITED_ORDERS}`;
    }

    ctx.reply(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    logUserSellOrdersError({
      error,
      userId: String(ctx.from?.id),
      chatId: String(ctx.chat?.id),
    });
    ctx.reply(
      MESSAGES.ORDERS.FETCH_ERROR_SELL,
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleContactSupportButton = (ctx: Context) => {
  ctx.reply(MESSAGES.SUPPORT.CONTACT_INFO, createMarketplaceInlineKeyboard());
};

export const handleDepositGiftButton = async (ctx: Context) => {
  const tgId = ctx.from?.id?.toString();

  if (!tgId) {
    ctx.reply(MESSAGES.TELEGRAM_ID_ERROR);
    return;
  }

  // Clear user session as it's no longer relevant after deposit operation
  try {
    await clearUserSession(tgId);
  } catch (error) {
    log.warn("Failed to clear user session on deposit gift", {
      operation: "deposit_gift_clear_session",
      tgId,
      error: error instanceof Error ? error.message : String(error),
    });
    // Continue with deposit operation even if session clear fails
  }

  if (isSimulationEnabled()) {
    const simulationService = getSimulationService();
    await simulationService.handleGiftDepositSimulation({
      ctx,
      tgId,
    });
  } else {
    // Normal mode - show instructions
    const message = `🎁 Deposit a Gift

You should go to ${PREM_RELAYER_USERNAME} and just deposit a gift. Then, you will see your deposited gift in the Pram app in the tab called 'My Gifts.'

Steps:
1. Go to ${PREM_RELAYER_USERNAME}
2. Send your gift to the relayer
3. Your gift will appear in the Pram app under 'My Gifts'

This allows you to deposit gifts without linking them to a specific order.`;

    ctx.reply(message, createMarketplaceInlineKeyboard());
  }
};
