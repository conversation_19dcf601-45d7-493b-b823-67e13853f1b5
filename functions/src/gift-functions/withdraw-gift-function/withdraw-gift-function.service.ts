import * as admin from "firebase-admin";
import {
  GIFTS_COLLECTION_NAME,
  ORDERS_COLLECTION_NAME,
  GiftEntity,
  GiftStatus,
  OrderEntity,
  OrderStatus,
  AppDate,
  formatDateToFirebaseTimestamp,
  TxType,
} from "../../mikerudenko/marketplace-shared";
import { getAppConfig } from "../../services/fee-service/fee-service";
import {
  getUserById,
  findUserIdByTgId,
} from "../../services/user-lookup.service";
import { updateGiftOwnership } from "../../services/gift-service";
import {
  getUserBalance,
  spendFundsWithHistory,
} from "../../services/balance-service/balance-service";

import { logger } from "firebase-functions/v2";

export interface WithdrawGiftParams {
  giftId: string;
  userTgId: string;
}

export interface WithdrawGiftResult {
  success: boolean;
  message?: string;
  error?: string;
  ownedGiftId?: string; // For successful withdrawals, return the owned_gift_id for transfer
}

export async function withdrawGiftService(
  params: WithdrawGiftParams
): Promise<WithdrawGiftResult> {
  const { giftId, userTgId } = params;
  const db = admin.firestore();

  try {
    // Get gift document
    const giftDoc = await db
      .collection(GIFTS_COLLECTION_NAME)
      .doc(giftId)
      .get();

    if (!giftDoc.exists) {
      return {
        success: false,
        error: "Gift not found",
      };
    }

    const gift = { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;

    // Validate gift belongs to user
    if (gift.owner_tg_id !== userTgId) {
      return {
        success: false,
        error: "Gift does not belong to this user",
      };
    }

    // Validate gift status
    if (gift.status !== GiftStatus.DEPOSITED) {
      return {
        success: false,
        error: "Gift is not available for withdrawal",
      };
    }

    // Get user data
    const userLookupResult = await findUserIdByTgId(userTgId);
    if (!userLookupResult.success || !userLookupResult.userId) {
      return {
        success: false,
        error: "User not found",
      };
    }

    const user = await getUserById(userLookupResult.userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    // Check if gift is linked to any order
    const ordersQuery = await db
      .collection(ORDERS_COLLECTION_NAME)
      .where("giftId", "==", giftId)
      .limit(1)
      .get();

    if (ordersQuery.empty) {
      // Case 3: Gift not linked to any order
      return await handleUnlinkedGiftWithdrawal(
        giftDoc,
        user.id,
        gift.owned_gift_id
      );
    }

    const orderDoc = ordersQuery.docs[0];
    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Case 1: Order with status "gift_sent_to_relayer" and user is buyer
    if (
      order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
      order.buyerId === user.id
    ) {
      return await handleBuyerWithdrawalFromActiveOrder(
        order,
        gift.owned_gift_id
      );
    }

    // Case 2: Order with status "cancelled" and user is seller
    if (order.status === OrderStatus.CANCELLED && order.sellerId === user.id) {
      return await handleSellerWithdrawalFromCancelledOrder(
        giftDoc,
        user.id,
        gift.owned_gift_id
      );
    }

    // Gift is linked to order but doesn't meet withdrawal criteria
    return {
      success: false,
      error: "Gift cannot be withdrawn in current order state",
    };
  } catch (error) {
    logger.error("Error in withdrawGiftService", error);
    return {
      success: false,
      error: "Internal error occurred during withdrawal",
    };
  }
}

async function handleBuyerWithdrawalFromActiveOrder(
  order: OrderEntity,
  ownedGiftId: string
): Promise<WithdrawGiftResult> {
  try {
    const db = admin.firestore();

    // Update order status to fulfilled
    await db
      .collection(ORDERS_COLLECTION_NAME)
      .doc(order.id!)
      .update({
        status: OrderStatus.FULFILLED,
        updatedAt: formatDateToFirebaseTimestamp(
          admin.firestore.Timestamp.now() as AppDate
        ),
      });

    // Transfer gift ownership to buyer (mark as withdrawn)
    if (order.giftId && order.buyerId) {
      const buyer = await getUserById(order.buyerId);
      if (buyer?.tg_id) {
        await updateGiftOwnership(
          order.giftId,
          buyer.tg_id,
          GiftStatus.WITHDRAWN
        );
      }
    }

    return {
      success: true,
      message: "Gift withdrawn successfully (purchase completed)",
      ownedGiftId,
    };
  } catch (error) {
    logger.error("Error in handleBuyerWithdrawalFromActiveOrder", error);
    return {
      success: false,
      error: "Failed to process buyer withdrawal",
    };
  }
}

async function handleSellerWithdrawalFromCancelledOrder(
  giftDoc: admin.firestore.DocumentSnapshot,
  userId: string,
  ownedGiftId: string
): Promise<WithdrawGiftResult> {
  return await handleWithdrawalWithFee(
    giftDoc,
    userId,
    "cancelled order",
    ownedGiftId
  );
}

async function handleUnlinkedGiftWithdrawal(
  giftDoc: admin.firestore.DocumentSnapshot,
  userId: string,
  ownedGiftId: string
): Promise<WithdrawGiftResult> {
  return await handleWithdrawalWithFee(
    giftDoc,
    userId,
    "unlinked gift",
    ownedGiftId
  );
}

async function handleWithdrawalWithFee(
  giftDoc: admin.firestore.DocumentSnapshot,
  userId: string,
  withdrawalType: string,
  ownedGiftId: string
): Promise<WithdrawGiftResult> {
  try {
    const config = await getAppConfig();
    const withdrawalFee = config.withdrawal_gift_fee || 0;

    // Get user data to check balance
    const userLookupResult = await findUserIdByTgId(userId);
    if (!userLookupResult.success || !userLookupResult.userId) {
      return {
        success: false,
        error: "User not found",
      };
    }

    const user = await getUserById(userLookupResult.userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    // Check if user has enough balance for fee
    const userBalance = await getUserBalance(user.id);
    const availableBalance = userBalance.sum - userBalance.locked;

    if (availableBalance < withdrawalFee) {
      return {
        success: false,
        error: "Insufficient balance to cover withdrawal fee",
      };
    }

    const db = admin.firestore();
    const batch = db.batch();

    // Deduct withdrawal fee from user balance
    if (withdrawalFee > 0) {
      await spendFundsWithHistory({
        userId: user.id,
        amount: withdrawalFee,
        txType: TxType.CANCELATION_FEE,
        description: `Gift withdrawal fee - ${withdrawalType}`,
        descriptionIntlKey: "transaction.withdrawal_fee",
        descriptionIntlParams: { type: withdrawalType },
      });
    }

    // Update gift status to withdrawn
    batch.update(giftDoc.ref, {
      status: GiftStatus.WITHDRAWN,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    await batch.commit();

    return {
      success: true,
      message: `Gift withdrawn successfully (${withdrawalType})`,
      ownedGiftId,
    };
  } catch (error) {
    logger.error("Error in handleWithdrawalWithFee", error);
    return {
      success: false,
      error: "Failed to process withdrawal with fee",
    };
  }
}
