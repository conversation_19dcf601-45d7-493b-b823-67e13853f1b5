import { firestore } from "firebase-admin";
import {
  GiftEntity,
  OrderStatus,
  GiftStatus,
  OrderEntity,
} from "../../mikerudenko/marketplace-shared";
import { logger } from "firebase-functions/v2";

const db = firestore();

export const getUserGiftsAvailableForWithdrawalService = async (
  tgId: string
): Promise<GiftEntity[]> => {
  try {
    logger.info("Starting getUserGiftsAvailableForWithdrawalService", { tgId });

    // Get all gifts owned by the user with status 'deposited'
    const giftsQuery = await db
      .collection("gifts")
      .where("owner_tg_id", "==", tgId)
      .where("status", "==", GiftStatus.DEPOSITED)
      .get();

    const gifts = giftsQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as GiftEntity[];

    logger.info("Found gifts for user", { tgId, totalGifts: gifts.length });

    // Get all orders to check gift relationships
    const ordersQuery = await db.collection("orders").get();
    const orders = ordersQuery.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    logger.info("Retrieved all orders for filtering", {
      totalOrders: orders.length,
    });

    // Filter gifts based on the requirements
    const availableGifts = gifts.filter((gift) => {
      // Find orders related to this gift
      const relatedOrders = orders.filter((order) => order.giftId === gift.id);

      if (relatedOrders.length === 0) {
        // Gift not linked to any order - available for withdrawal
        logger.debug("Gift not linked to any order", { giftId: gift.id });
        return true;
      }

      // Check each related order
      for (const order of relatedOrders) {
        // Gifts linked to orders with status 'gift_sent_to_relayer' where user is buyer
        if (
          order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
          order.buyerId === tgId
        ) {
          logger.debug(
            "Gift linked to gift_sent_to_relayer order where user is buyer",
            {
              giftId: gift.id,
              orderId: order.id,
            }
          );
          return true;
        }

        // Gifts linked to cancelled orders where user is seller
        if (order.status === OrderStatus.CANCELLED && order.sellerId === tgId) {
          logger.debug("Gift linked to cancelled order where user is seller", {
            giftId: gift.id,
            orderId: order.id,
          });
          return true;
        }

        // Gifts not linked to orders with status 'active' where user is seller
        // This means if there's an active order where user is seller, gift is NOT available
        if (order.status === OrderStatus.ACTIVE && order.sellerId === tgId) {
          logger.debug(
            "Gift linked to active order where user is seller - not available",
            {
              giftId: gift.id,
              orderId: order.id,
            }
          );
          return false;
        }
      }

      // If none of the exclusion conditions are met, gift is available
      return true;
    });

    logger.info("Filtered gifts available for withdrawal", {
      tgId,
      availableGiftsCount: availableGifts.length,
    });

    return availableGifts;
  } catch (error) {
    logger.error("Error in getUserGiftsAvailableForWithdrawalService", {
      tgId,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
};
