# Marketplace Bot

A simple Telegram bot for the marketplace platform that handles order management and gift echo functionality.

## Features

- **Order Management**: View and complete marketplace orders
- **Gift Echo**: Simple gift echo system using Telegram Bot API transferGift method
- **Referral System**: Generate and share referral links
- **Business Account Integration**: Uses Telegram Business Account for gift transfers
- **Cloud Function Session Storage**: Persistent session management using Firebase Cloud Functions for scalability
- **Webhook Support**: Automatic webhook setup for Cloud Run deployment with SIGTERM handling
- **Health Monitoring**: Built-in health checks and monitoring endpoints

## Setup

1. **Install Dependencies**

   ```bash
   bun install
   ```

2. **Get Bot Token**
   - Message [@BotFather](https://t.me/botfather) on Telegram
   - Create a new bot with `/newbot`
   - Copy the bot token to your `.env.local` file

3. **Set Up Business Account**
   - Connect your bot to a Telegram Business Account
   - The business connection ID will be automatically provided in gift messages

4. **Start the Bot**

   ```bash
   # Development mode (polling)
   npm run dev

   # Production mode (webhook)
   npm run build
   npm start
   ```
